#!/bin/bash

# 显示延迟统计表格的脚本
# 功能：以更清晰的格式显示统计结果

echo "======================================"
echo "           延迟统计汇总表"
echo "======================================"
echo
printf "%-8s %-15s %-15s\n" "文件" "latency平均值" "latency标准差"
echo "----------------------------------------------"
printf "%-8s %-15s %-15s\n" "文件1" "28.65" "377.77"
printf "%-8s %-15s %-15s\n" "文件2" "33.64" "409.30"
printf "%-8s %-15s %-15s\n" "文件3" "127.53" "308.63"
printf "%-8s %-15s %-15s\n" "文件4" "2661.47" "10985.93"
printf "%-8s %-15s %-15s\n" "文件5" "2710.32" "10510.20"
echo "----------------------------------------------"
echo
echo "说明："
echo "文件1: 20250825_165124_data.csv"
echo "文件2: 20250825_165125_data.csv"
echo "文件3: 20250825_165128_data.csv"
echo "文件4: 20250825_165130_data.csv"
echo "文件5: 20250825_165131_data.csv"
echo
echo "整体统计："
echo "- 总记录数: 5,460,921"
echo "- 整体平均值: 2491.60"
echo "- 整体标准差: 10372.55"
echo "- 最小值: 4.00"
echo "- 最大值: 153117.00"
