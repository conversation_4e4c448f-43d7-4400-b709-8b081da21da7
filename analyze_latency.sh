#!/bin/bash

# 延迟数据分析脚本
# 功能：计算每个CSV文件中latency列的平均值和标准差

echo "==================================="
echo "延迟数据统计分析"
echo "==================================="
echo

# 创建结果文件
result_file="latency_summary.csv"
echo "文件名,条数,latency平均值,latency标准差" > "$result_file"

# 创建格式化的表格文件
table_file="latency_summary_table.txt"
echo "延迟统计汇总表" > "$table_file"
echo "==================================" >> "$table_file"
printf "%-25s %-10s %-15s %-15s\n" "文件" "条数" "latency平均值" "latency标准差" >> "$table_file"
echo "--------------------------------------------------------------------------" >> "$table_file"

# 函数：计算平均值和标准差
calculate_stats() {
    local file=$1
    local filename=$(basename "$file")
    
    echo "正在处理文件: $filename"
    
    # 提取latency列数据（跳过标题行）
    latency_data=$(tail -n +2 "$file" | cut -d',' -f2)
    
    # 计算总条数
    total_count=$(echo "$latency_data" | wc -l)
    
    # 使用awk计算平均值和标准差
    stats=$(echo "$latency_data" | awk '
    {
        sum += $1
        sumsq += $1*$1
        count++
    }
    END {
        if (count > 0) {
            mean = sum/count
            variance = (sumsq/count) - (mean*mean)
            stddev = sqrt(variance)
            printf "%.2f,%.2f", mean, stddev
        } else {
            printf "0,0"
        }
    }')
    
    # 分离平均值和标准差
    mean=$(echo "$stats" | cut -d',' -f1)
    stddev=$(echo "$stats" | cut -d',' -f2)
    
    # 写入CSV结果文件
    echo "$filename,$total_count,$mean,$stddev" >> "$result_file"
    
    # 写入格式化表格文件
    printf "%-25s %-10s %-15s %-15s\n" "$filename" "$total_count" "$mean" "$stddev" >> "$table_file"
    
    echo "  条数: $total_count"
    echo "  平均值: $mean"
    echo "  标准差: $stddev"
    echo
}

# 处理所有CSV文件（排除汇总文件）
echo "开始分析CSV文件..."
echo

for file in *.csv; do
    if [[ -f "$file" && "$file" != "$result_file" ]]; then
        calculate_stats "$file"
    fi
done

echo "==================================="
echo "分析完成！"
echo "结果已保存到以下文件："
echo "- CSV格式: $result_file"
echo "- 表格格式: $table_file"
echo "==================================="
echo

# 显示汇总表格
echo "延迟统计汇总："
cat "$table_file"
echo

# 显示一些额外的统计信息
echo "==================================="
echo "额外统计信息："
echo "==================================="

# 计算所有文件的整体统计
echo "正在计算所有文件的整体统计..."
all_latency_data=""
total_records=0

for file in *.csv; do
    if [[ -f "$file" && "$file" != "$result_file" ]]; then
        file_data=$(tail -n +2 "$file" | cut -d',' -f2)
        all_latency_data="$all_latency_data$file_data"$'\n'
        file_records=$(tail -n +2 "$file" | wc -l)
        total_records=$((total_records + file_records))
        echo "文件 $(basename "$file"): $file_records 条记录"
    fi
done

# 计算整体统计
overall_stats=$(echo "$all_latency_data" | grep -v '^$' | awk '
{
    sum += $1
    sumsq += $1*$1
    count++
    if (NR == 1) {
        min = max = $1
    } else {
        if ($1 < min) min = $1
        if ($1 > max) max = $1
    }
}
END {
    if (count > 0) {
        mean = sum/count
        variance = (sumsq/count) - (mean*mean)
        stddev = sqrt(variance)
        printf "总记录数: %d\n", count
        printf "整体平均值: %.2f\n", mean
        printf "整体标准差: %.2f\n", stddev
        printf "最小值: %.2f\n", min
        printf "最大值: %.2f\n", max
    }
}')

echo
echo "$overall_stats"
echo
echo "分析脚本执行完毕！"
